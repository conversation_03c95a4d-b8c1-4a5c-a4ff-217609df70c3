import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Star, Award } from 'lucide-react';
import { Link } from 'react-router-dom';
import { Driver } from '../../types';
import Card from '../ui/Card';
import Button from '../ui/Button';
import { formatPrice } from '../../utils/currency';
import CurrencyIcon from '../ui/CurrencyIcon';

interface DriverCardProps {
  driver: Driver;
}

const DriverCard: React.FC<DriverCardProps> = ({ driver }) => {
  return (
    <Card className="h-full flex flex-col">
      <div className="relative">
        <Card.Image
          src={driver.profileImage}
          alt={driver.name}
          className="h-64 object-cover"
        />
        <div className="absolute bottom-2 right-2 bg-white px-2 py-1 rounded-full flex items-center shadow-md">
          <Star size={16} className="text-yellow-500 mr-1" />
          <span className="font-medium">{driver.rating.toFixed(1)}</span>
          <span className="text-xs text-gray-500 ml-1">({driver.reviews})</span>
        </div>
      </div>
      <Card.Content className="flex-grow">
        <Card.Title>
          {driver.name}
        </Card.Title>
        <div className="flex items-center mb-2">
          <Award size={16} className="text-primary-600 mr-1" />
          <span className="text-sm">{driver.experience} years experience</span>
        </div>
        <div className="mb-4 space-y-2">
          <div className="flex items-center text-gray-600">
            <MapPin size={16} className="mr-1 flex-shrink-0" />
            <span className="text-sm truncate">{driver.location}</span>
          </div>
          <div className="flex items-center text-gray-600">
            <Clock size={16} className="mr-1 flex-shrink-0" />
            <span className="text-sm truncate">{driver.availabilityNotes}</span>
          </div>
          <div className="flex items-center font-medium text-primary-700">
            <CurrencyIcon size={16} className="mr-1 flex-shrink-0" />
            <span>{formatPrice(driver.pricePerHour, 'hour')}</span>
          </div>
        </div>
        <div className="flex flex-wrap gap-1 mb-4">
          {driver.specialties.map((specialty, index) => (
            <span
              key={index}
              className="inline-block bg-gray-100 rounded-full px-2 py-1 text-xs font-medium text-gray-700"
            >
              {specialty}
            </span>
          ))}
        </div>
      </Card.Content>
      <Card.Footer className="mt-auto">
        <Link to={`/drivers/${driver.id}`} className="w-full">
          <Button variant="primary" fullWidth>
            Book Now
          </Button>
        </Link>
      </Card.Footer>
    </Card>
  );
};

export default DriverCard;
