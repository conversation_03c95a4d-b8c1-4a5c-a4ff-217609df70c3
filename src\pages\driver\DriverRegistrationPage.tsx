import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Upload, Check, AlertCircle } from 'lucide-react';
import MainLayout from '../../components/layout/MainLayout';
import Button from '../../components/ui/Button';
import { useDrivers } from '../../context/DriverContext';
import { useAuth } from '../../context/AuthContext';
import Spinner from '../../components/ui/Spinner';

const DriverRegistrationPage: React.FC = () => {
  const navigate = useNavigate();
  const { addDriver } = useDrivers();
  const { user, isAuthenticated } = useAuth();

  const [formData, setFormData] = useState({
    name: user?.name || '',
    age: '',
    experience: '',
    licenseNumber: '',
    location: '',
    pricePerHour: '',
    specialties: [] as string[],
    availabilityNotes: '',
  });

  const [profileImage, setProfileImage] = useState<string>('');
  const [profileImageFile, setProfileImageFile] = useState<File | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const specialtyOptions = [
    'City Driving',
    'Long Distance',
    'Airport Transfers',
    'Event Transportation',
    'Tourist Guide',
    'Luxury Vehicles',
    'Corporate Events',
  ];

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSpecialtyChange = (specialty: string) => {
    setFormData(prev => {
      const specialties = [...prev.specialties];

      if (specialties.includes(specialty)) {
        return {
          ...prev,
          specialties: specialties.filter(s => s !== specialty),
        };
      } else {
        return {
          ...prev,
          specialties: [...specialties, specialty],
        };
      }
    });
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setProfileImageFile(file);
      // Create preview URL
      const reader = new FileReader();
      reader.onload = (e) => {
        setProfileImage(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!isAuthenticated || !user) {
      navigate('/login');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      // Validate form data
      if (formData.specialties.length === 0) {
        throw new Error('Please select at least one specialty');
      }

      // Create driver
      await addDriver({
        userId: user.id,
        name: formData.name,
        age: parseInt(formData.age),
        experience: parseInt(formData.experience),
        profileImage: '', // Will be set by backend after upload
        licenseNumber: formData.licenseNumber,
        licenseVerificationStatus: 'pending',
        location: formData.location,
        pricePerHour: parseFloat(formData.pricePerHour),
        rating: 0,
        reviews: 0,
        specialties: formData.specialties,
        availabilityNotes: formData.availabilityNotes,
        isAvailable: true,
      }, profileImageFile || undefined);

      setSuccess(true);

      // Reset form
      setFormData({
        name: '',
        age: '',
        experience: '',
        licenseNumber: '',
        location: '',
        pricePerHour: '',
        specialties: [],
        availabilityNotes: '',
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to register as a driver');
      console.error(err);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Register as a Driver</h1>
          <p className="text-gray-600 mb-6">
            Fill out the form below to register as a driver on our platform.
          </p>

          {success ? (
            <div className="bg-success-50 border border-success-200 rounded-lg p-6 text-center">
              <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-success-100 text-success-600 mb-4">
                <Check size={24} />
              </div>
              <h2 className="text-xl font-medium text-success-800 mb-2">Registration Successful!</h2>
              <p className="text-success-600 mb-6">
                Your driver profile has been created. Your license verification is pending.
              </p>
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <Button onClick={() => navigate('/drivers')}>
                  Browse Drivers
                </Button>
                <Button variant="outline" onClick={() => navigate('/account')}>
                  Go to My Account
                </Button>
              </div>
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow-md p-6">
              {error && (
                <div className="mb-6 bg-error-50 border border-error-200 rounded-lg p-4 flex items-start">
                  <AlertCircle size={20} className="text-error-600 mr-2 flex-shrink-0 mt-0.5" />
                  <div className="text-error-700">{error}</div>
                </div>
              )}

              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Profile Image
                </label>
                <div className="flex items-center">
                  <div className="w-20 h-20 rounded-full overflow-hidden mr-4 bg-gray-100">
                    {profileImage ? (
                      <img
                        src={profileImage}
                        alt="Profile"
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center text-gray-400">
                        <Upload size={24} />
                      </div>
                    )}
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 mb-2">
                      Upload a professional photo of yourself.
                    </p>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      className="relative overflow-hidden"
                    >
                      Upload Photo
                      <input
                        type="file"
                        className="absolute inset-0 opacity-0 cursor-pointer"
                        accept="image/*"
                        onChange={handleImageUpload}
                      />
                    </Button>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                    Full Name
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    required
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    value={formData.name}
                    onChange={handleChange}
                  />
                </div>

                <div>
                  <label htmlFor="age" className="block text-sm font-medium text-gray-700 mb-1">
                    Age
                  </label>
                  <input
                    type="number"
                    id="age"
                    name="age"
                    min="18"
                    required
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    value={formData.age}
                    onChange={handleChange}
                  />
                </div>

                <div>
                  <label htmlFor="experience" className="block text-sm font-medium text-gray-700 mb-1">
                    Driving Experience (years)
                  </label>
                  <input
                    type="number"
                    id="experience"
                    name="experience"
                    min="1"
                    required
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    value={formData.experience}
                    onChange={handleChange}
                  />
                </div>

                <div>
                  <label htmlFor="licenseNumber" className="block text-sm font-medium text-gray-700 mb-1">
                    Driver's License Number
                  </label>
                  <input
                    type="text"
                    id="licenseNumber"
                    name="licenseNumber"
                    required
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    value={formData.licenseNumber}
                    onChange={handleChange}
                  />
                </div>

                <div>
                  <label htmlFor="location" className="block text-sm font-medium text-gray-700 mb-1">
                    Location
                  </label>
                  <input
                    type="text"
                    id="location"
                    name="location"
                    required
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    value={formData.location}
                    onChange={handleChange}
                  />
                </div>

                <div>
                  <label htmlFor="pricePerHour" className="block text-sm font-medium text-gray-700 mb-1">
                    Hourly Rate ($)
                  </label>
                  <input
                    type="number"
                    id="pricePerHour"
                    name="pricePerHour"
                    min="10"
                    step="0.01"
                    required
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    value={formData.pricePerHour}
                    onChange={handleChange}
                  />
                </div>
              </div>

              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Specialties
                </label>
                <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
                  {specialtyOptions.map((specialty) => (
                    <div key={specialty} className="flex items-center">
                      <input
                        type="checkbox"
                        id={`specialty-${specialty}`}
                        className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                        checked={formData.specialties.includes(specialty)}
                        onChange={() => handleSpecialtyChange(specialty)}
                      />
                      <label htmlFor={`specialty-${specialty}`} className="ml-2 text-sm text-gray-700">
                        {specialty}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              <div className="mb-6">
                <label htmlFor="availabilityNotes" className="block text-sm font-medium text-gray-700 mb-1">
                  Availability Notes
                </label>
                <textarea
                  id="availabilityNotes"
                  name="availabilityNotes"
                  rows={3}
                  required
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="e.g., Available weekdays from 9 AM to 5 PM"
                  value={formData.availabilityNotes}
                  onChange={handleChange}
                />
              </div>

              <div className="flex justify-end">
                <Button
                  type="submit"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? <Spinner size="sm" className="mr-2" /> : null}
                  Register as Driver
                </Button>
              </div>
            </form>
          )}
        </div>
      </div>
    </MainLayout>
  );
};

export default DriverRegistrationPage;
