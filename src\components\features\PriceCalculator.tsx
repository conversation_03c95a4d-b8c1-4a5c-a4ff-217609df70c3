import React from 'react';
import { Clock, Calendar } from 'lucide-react';
import { formatCurrency } from '../../utils/currency';
import CurrencyIcon from '../ui/CurrencyIcon';

interface PriceCalculatorProps {
  hourlyRate: number;
}

const PriceCalculator: React.FC<PriceCalculatorProps> = ({ hourlyRate }) => {
  // Calculate rates based on hourly rate
  const dailyRate = hourlyRate * 8;
  const weeklyRate = dailyRate * 5;
  const monthlyRate = weeklyRate * 4;

  const rates = [
    { period: 'Hour', rate: hourlyRate, icon: <Clock size={18} /> },
    { period: 'Day', rate: dailyRate, icon: <Calendar size={18} /> },
    { period: 'Week', rate: weeklyRate, icon: <Calendar size={18} /> },
    { period: 'Month', rate: monthlyRate, icon: <Calendar size={18} /> },
  ];

  return (
    <div className="mt-4">
      <h3 className="text-lg font-medium text-gray-900 mb-3">Pricing</h3>
      <div className="grid grid-cols-2 gap-3 sm:grid-cols-4">
        {rates.map((item) => (
          <div
            key={item.period}
            className="bg-white p-3 rounded-lg border border-gray-200 shadow-sm"
          >
            <div className="flex items-center text-sm text-gray-500 mb-1">
              {item.icon}
              <span className="ml-1">Per {item.period}</span>
            </div>
            <div className="flex items-center">
              <CurrencyIcon size={18} className="text-primary-600" />
              <span className="text-xl font-semibold text-gray-900">
                {formatCurrency(item.rate)}
              </span>
            </div>
          </div>
        ))}
      </div>
      <p className="text-sm text-gray-500 mt-2">
        Actual rates may vary. Discuss with the owner for exact pricing.
      </p>
    </div>
  );
};

export default PriceCalculator;