import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { UserRound, Calendar, Clock, DollarSign, MapPin, Star, ToggleLeft, Edit, AlertCircle, CheckCircle, XCircle } from 'lucide-react';
import MainLayout from '../../components/layout/MainLayout';
import Button from '../../components/ui/Button';
import Card from '../../components/ui/Card';
import { useDrivers } from '../../context/DriverContext';
import { useBookings } from '../../context/BookingContext';
import { useAuth } from '../../context/AuthContext';
import Spinner from '../../components/ui/Spinner';
import { Booking } from '../../types';

const DriverDashboardPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { drivers, toggleDriverAvailability, updateDriver } = useDrivers();
  const { bookings, updateBookingStatus } = useBookings();
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'bookings' | 'profile'>('bookings');
  
  // Find the driver profile for the current user
  const driverProfile = drivers.find(driver => driver.userId === user?.id);
  
  // Filter bookings for this driver
  const driverBookings = bookings.filter(
    booking => booking.itemType === 'driver' && booking.itemId === driverProfile?.id
  );
  
  const handleToggleAvailability = async () => {
    if (!driverProfile) return;
    
    setIsLoading(true);
    try {
      await toggleDriverAvailability(driverProfile.id);
    } catch (error) {
      console.error('Failed to toggle availability:', error);
    } finally {
      setIsLoading(false);
    }
  };
  
  const handleUpdateBookingStatus = async (bookingId: string, status: Booking['status']) => {
    setIsLoading(true);
    try {
      await updateBookingStatus(bookingId, status);
    } catch (error) {
      console.error('Failed to update booking status:', error);
    } finally {
      setIsLoading(false);
    }
  };
  
  if (!user || user.role !== 'driver') {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="bg-error-50 border border-error-200 rounded-lg p-4 text-center">
            <h2 className="text-xl font-medium text-error-800 mb-2">Access Denied</h2>
            <p className="text-error-600">
              You must be logged in as a driver to access this page.
            </p>
            <Button 
              className="mt-4"
              onClick={() => navigate('/login')}
            >
              Log In
            </Button>
          </div>
        </div>
      </MainLayout>
    );
  }
  
  if (!driverProfile) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="bg-warning-50 border border-warning-200 rounded-lg p-4 text-center">
            <h2 className="text-xl font-medium text-warning-800 mb-2">Driver Profile Not Found</h2>
            <p className="text-warning-600">
              You need to complete your driver registration first.
            </p>
            <Button 
              className="mt-4"
              onClick={() => navigate('/driver/register')}
            >
              Complete Registration
            </Button>
          </div>
        </div>
      </MainLayout>
    );
  }
  
  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Driver Dashboard</h1>
            <p className="text-gray-600">
              Manage your driver profile and booking requests.
            </p>
          </div>
          
          <div className="mt-4 md:mt-0 flex items-center">
            <div className={`mr-4 px-4 py-2 rounded-full flex items-center ${
              driverProfile.isAvailable 
                ? 'bg-success-100 text-success-800' 
                : 'bg-gray-100 text-gray-800'
            }`}>
              <span className="mr-2">Status:</span>
              <span className="font-medium">
                {driverProfile.isAvailable ? 'Available' : 'Unavailable'}
              </span>
            </div>
            
            <Button
              variant={driverProfile.isAvailable ? 'outline' : 'primary'}
              onClick={handleToggleAvailability}
              disabled={isLoading}
            >
              {isLoading ? (
                <Spinner size="sm" className="mr-2" />
              ) : (
                <ToggleLeft size={18} className="mr-2" />
              )}
              {driverProfile.isAvailable ? 'Go Offline' : 'Go Online'}
            </Button>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-md overflow-hidden mb-8">
          <div className="flex border-b border-gray-200">
            <button
              className={`px-6 py-3 font-medium text-sm focus:outline-none ${
                activeTab === 'bookings'
                  ? 'text-primary-600 border-b-2 border-primary-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
              onClick={() => setActiveTab('bookings')}
            >
              Booking Requests
            </button>
            <button
              className={`px-6 py-3 font-medium text-sm focus:outline-none ${
                activeTab === 'profile'
                  ? 'text-primary-600 border-b-2 border-primary-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
              onClick={() => setActiveTab('profile')}
            >
              Driver Profile
            </button>
          </div>
          
          {activeTab === 'bookings' && (
            <div className="p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-4">Your Booking Requests</h2>
              
              {driverBookings.length === 0 ? (
                <div className="text-center py-8 border border-dashed border-gray-300 rounded-lg">
                  <Calendar size={48} className="mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No booking requests yet</h3>
                  <p className="text-gray-600">
                    When clients book your services, their requests will appear here.
                  </p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Client
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Date & Time
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Duration
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Price
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {driverBookings.map((booking) => {
                        const startDate = new Date(booking.startTime);
                        const endDate = new Date(booking.endTime);
                        const durationHours = (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60);
                        
                        return (
                          <tr key={booking.id}>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center">
                                <UserRound size={24} className="text-gray-400 mr-2" />
                                <div className="text-sm font-medium text-gray-900">
                                  Client #{booking.userId.slice(-4)}
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="text-sm text-gray-900">
                                {startDate.toLocaleDateString()}
                              </div>
                              <div className="text-sm text-gray-500">
                                {startDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="text-sm text-gray-900">
                                {durationHours} {durationHours === 1 ? 'hour' : 'hours'}
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="text-sm font-medium text-gray-900">
                                ${booking.totalPrice.toFixed(2)}
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                booking.status === 'pending'
                                  ? 'bg-yellow-100 text-yellow-800'
                                  : booking.status === 'confirmed'
                                  ? 'bg-blue-100 text-blue-800'
                                  : booking.status === 'completed'
                                  ? 'bg-success-100 text-success-800'
                                  : 'bg-error-100 text-error-800'
                              }`}>
                                {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                              {booking.status === 'pending' && (
                                <div className="flex space-x-2">
                                  <button
                                    className="text-success-600 hover:text-success-800"
                                    onClick={() => handleUpdateBookingStatus(booking.id, 'confirmed')}
                                  >
                                    <CheckCircle size={18} />
                                  </button>
                                  <button
                                    className="text-error-600 hover:text-error-800"
                                    onClick={() => handleUpdateBookingStatus(booking.id, 'cancelled')}
                                  >
                                    <XCircle size={18} />
                                  </button>
                                </div>
                              )}
                              {booking.status === 'confirmed' && (
                                <button
                                  className="text-success-600 hover:text-success-800"
                                  onClick={() => handleUpdateBookingStatus(booking.id, 'completed')}
                                >
                                  Complete
                                </button>
                              )}
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          )}
          
          {activeTab === 'profile' && (
            <div className="p-6">
              <div className="flex flex-col md:flex-row">
                <div className="md:w-1/3 mb-6 md:mb-0 md:pr-6">
                  <div className="bg-gray-100 rounded-lg p-6 text-center">
                    <div className="w-32 h-32 mx-auto rounded-full overflow-hidden mb-4">
                      <img
                        src={driverProfile.profileImage}
                        alt={driverProfile.name}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 mb-1">{driverProfile.name}</h3>
                    <div className="flex items-center justify-center mb-4">
                      <Star size={16} className="text-yellow-500 mr-1" />
                      <span className="font-medium">{driverProfile.rating.toFixed(1)}</span>
                      <span className="text-xs text-gray-500 ml-1">({driverProfile.reviews} reviews)</span>
                    </div>
                    <Link to="/driver/edit-profile">
                      <Button variant="outline" className="w-full flex items-center justify-center">
                        <Edit size={16} className="mr-2" />
                        Edit Profile
                      </Button>
                    </Link>
                  </div>
                </div>
                
                <div className="md:w-2/3">
                  <h3 className="text-lg font-bold text-gray-900 mb-4">Driver Information</h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="text-sm text-gray-500 mb-1">Age</div>
                      <div className="font-medium">{driverProfile.age} years</div>
                    </div>
                    
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="text-sm text-gray-500 mb-1">Experience</div>
                      <div className="font-medium">{driverProfile.experience} years</div>
                    </div>
                    
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="text-sm text-gray-500 mb-1">License Number</div>
                      <div className="font-medium">{driverProfile.licenseNumber}</div>
                    </div>
                    
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="text-sm text-gray-500 mb-1">License Status</div>
                      <div className={`font-medium ${
                        driverProfile.licenseVerificationStatus === 'verified'
                          ? 'text-success-600'
                          : driverProfile.licenseVerificationStatus === 'pending'
                          ? 'text-yellow-600'
                          : 'text-error-600'
                      }`}>
                        {driverProfile.licenseVerificationStatus.charAt(0).toUpperCase() + 
                         driverProfile.licenseVerificationStatus.slice(1)}
                      </div>
                    </div>
                  </div>
                  
                  <div className="mb-6">
                    <h4 className="text-md font-bold text-gray-900 mb-2">Location</h4>
                    <div className="flex items-center text-gray-700">
                      <MapPin size={18} className="mr-2 text-gray-500" />
                      {driverProfile.location}
                    </div>
                  </div>
                  
                  <div className="mb-6">
                    <h4 className="text-md font-bold text-gray-900 mb-2">Availability</h4>
                    <div className="flex items-center text-gray-700">
                      <Clock size={18} className="mr-2 text-gray-500" />
                      {driverProfile.availabilityNotes}
                    </div>
                  </div>
                  
                  <div className="mb-6">
                    <h4 className="text-md font-bold text-gray-900 mb-2">Hourly Rate</h4>
                    <div className="flex items-center text-gray-700">
                      <DollarSign size={18} className="mr-2 text-gray-500" />
                      ${driverProfile.pricePerHour}/hour
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="text-md font-bold text-gray-900 mb-2">Specialties</h4>
                    <div className="flex flex-wrap gap-2">
                      {driverProfile.specialties.map((specialty, index) => (
                        <span
                          key={index}
                          className="inline-block bg-gray-100 rounded-full px-3 py-1 text-sm font-medium text-gray-700"
                        >
                          {specialty}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  );
};

export default DriverDashboardPage;
