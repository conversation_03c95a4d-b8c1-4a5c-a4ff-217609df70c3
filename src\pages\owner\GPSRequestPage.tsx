import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { MapPin, Car, Phone, Mail, User, Calendar, ArrowLeft } from 'lucide-react';
import MainLayout from '../../components/layout/MainLayout';
import Button from '../../components/ui/Button';
import { useAuth } from '../../context/AuthContext';

const GPSRequestPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const [formData, setFormData] = useState({
    carMake: '',
    carModel: '',
    carYear: new Date().getFullYear(),
    carPlateNumber: '',
    ownerName: user?.name || '',
    ownerPhone: '',
    ownerEmail: user?.email || '',
    installationLocation: '',
    preferredDate: '',
    preferredTime: '',
    additionalNotes: '',
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: name === 'carYear' ? Number(value) : value,
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    // Validate required fields
    if (!formData.carMake || !formData.carModel || !formData.carPlateNumber || 
        !formData.ownerName || !formData.ownerPhone || !formData.installationLocation) {
      setError('Please fill in all required fields');
      return;
    }

    setIsSubmitting(true);

    try {
      // Here you would typically send the GPS installation request to your backend
      // For now, we'll simulate a successful submission
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setSuccess(true);
      
      // Redirect after a short delay
      setTimeout(() => {
        navigate('/owner/dashboard');
      }, 3000);
    } catch (err) {
      setError('Failed to submit GPS installation request. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (success) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-2xl mx-auto">
            <div className="bg-success-50 border border-success-200 rounded-lg p-8 text-center">
              <div className="w-16 h-16 bg-success-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <MapPin size={32} className="text-success-600" />
              </div>
              <h2 className="text-2xl font-bold text-success-800 mb-4">
                GPS Installation Request Submitted!
              </h2>
              <p className="text-success-700 mb-6">
                Thank you for your GPS installation request. Our team will contact you within 24-48 hours 
                to schedule the installation and provide you with pricing details.
              </p>
              <div className="space-y-2 text-sm text-success-600 mb-6">
                <p><strong>What's next?</strong></p>
                <p>• Our technician will call you to confirm details</p>
                <p>• We'll schedule a convenient installation time</p>
                <p>• Professional GPS installation at your location</p>
                <p>• Setup and training on the GPS tracking system</p>
              </div>
              <Button onClick={() => navigate('/owner/dashboard')}>
                Return to Dashboard
              </Button>
            </div>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-3xl mx-auto">
          <div className="flex items-center mb-6">
            <button
              onClick={() => navigate('/owner/dashboard')}
              className="text-primary-600 hover:text-primary-800 mr-4 flex items-center"
            >
              <ArrowLeft size={20} className="mr-1" />
              Back to Dashboard
            </button>
            <h1 className="text-3xl font-bold text-gray-900">GPS Installation Request</h1>
          </div>

          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="p-6 bg-primary-50 border-b border-primary-100">
              <h2 className="text-xl font-semibold text-primary-800 mb-2">
                Professional GPS Tracking Installation
              </h2>
              <p className="text-primary-700">
                Enhance your car's security and tracking capabilities with our professional GPS installation service.
                Perfect for car owners who want to monitor their vehicle's location and usage.
              </p>
            </div>

            <form onSubmit={handleSubmit} className="p-6">
              {error && (
                <div className="mb-6 p-4 bg-error-50 border border-error-200 rounded-lg">
                  <p className="text-error-700">{error}</p>
                </div>
              )}

              {/* Vehicle Information */}
              <div className="mb-8">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <Car size={20} className="mr-2 text-primary-600" />
                  Vehicle Information
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="carMake" className="block text-sm font-medium text-gray-700 mb-1">
                      Car Make <span className="text-error-600">*</span>
                    </label>
                    <input
                      type="text"
                      id="carMake"
                      name="carMake"
                      value={formData.carMake}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                      placeholder="e.g., Toyota"
                      required
                    />
                  </div>

                  <div>
                    <label htmlFor="carModel" className="block text-sm font-medium text-gray-700 mb-1">
                      Car Model <span className="text-error-600">*</span>
                    </label>
                    <input
                      type="text"
                      id="carModel"
                      name="carModel"
                      value={formData.carModel}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                      placeholder="e.g., Camry"
                      required
                    />
                  </div>

                  <div>
                    <label htmlFor="carYear" className="block text-sm font-medium text-gray-700 mb-1">
                      Year <span className="text-error-600">*</span>
                    </label>
                    <select
                      id="carYear"
                      name="carYear"
                      value={formData.carYear}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                      required
                    >
                      {Array.from({ length: 30 }, (_, i) => new Date().getFullYear() - i).map(year => (
                        <option key={year} value={year}>{year}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label htmlFor="carPlateNumber" className="block text-sm font-medium text-gray-700 mb-1">
                      Plate Number <span className="text-error-600">*</span>
                    </label>
                    <input
                      type="text"
                      id="carPlateNumber"
                      name="carPlateNumber"
                      value={formData.carPlateNumber}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                      placeholder="e.g., RAD 123A"
                      required
                    />
                  </div>
                </div>
              </div>

              {/* Owner Contact Information */}
              <div className="mb-8">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <User size={20} className="mr-2 text-primary-600" />
                  Contact Information
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="ownerName" className="block text-sm font-medium text-gray-700 mb-1">
                      Full Name <span className="text-error-600">*</span>
                    </label>
                    <input
                      type="text"
                      id="ownerName"
                      name="ownerName"
                      value={formData.ownerName}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                      required
                    />
                  </div>

                  <div>
                    <label htmlFor="ownerPhone" className="block text-sm font-medium text-gray-700 mb-1">
                      Phone Number <span className="text-error-600">*</span>
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Phone size={18} className="text-gray-400" />
                      </div>
                      <input
                        type="tel"
                        id="ownerPhone"
                        name="ownerPhone"
                        value={formData.ownerPhone}
                        onChange={handleInputChange}
                        className="w-full pl-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="+250 78 123 4567"
                        required
                      />
                    </div>
                  </div>

                  <div className="md:col-span-2">
                    <label htmlFor="ownerEmail" className="block text-sm font-medium text-gray-700 mb-1">
                      Email Address
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Mail size={18} className="text-gray-400" />
                      </div>
                      <input
                        type="email"
                        id="ownerEmail"
                        name="ownerEmail"
                        value={formData.ownerEmail}
                        onChange={handleInputChange}
                        className="w-full pl-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Installation Details */}
              <div className="mb-8">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <Calendar size={20} className="mr-2 text-primary-600" />
                  Installation Details
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="md:col-span-2">
                    <label htmlFor="installationLocation" className="block text-sm font-medium text-gray-700 mb-1">
                      Installation Location <span className="text-error-600">*</span>
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <MapPin size={18} className="text-gray-400" />
                      </div>
                      <input
                        type="text"
                        id="installationLocation"
                        name="installationLocation"
                        value={formData.installationLocation}
                        onChange={handleInputChange}
                        className="w-full pl-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="Address where installation should take place"
                        required
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="preferredDate" className="block text-sm font-medium text-gray-700 mb-1">
                      Preferred Date
                    </label>
                    <input
                      type="date"
                      id="preferredDate"
                      name="preferredDate"
                      value={formData.preferredDate}
                      onChange={handleInputChange}
                      min={new Date().toISOString().split('T')[0]}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    />
                  </div>

                  <div>
                    <label htmlFor="preferredTime" className="block text-sm font-medium text-gray-700 mb-1">
                      Preferred Time
                    </label>
                    <select
                      id="preferredTime"
                      name="preferredTime"
                      value={formData.preferredTime}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                      <option value="">Select time</option>
                      <option value="morning">Morning (8:00 AM - 12:00 PM)</option>
                      <option value="afternoon">Afternoon (12:00 PM - 5:00 PM)</option>
                      <option value="evening">Evening (5:00 PM - 8:00 PM)</option>
                    </select>
                  </div>

                  <div className="md:col-span-2">
                    <label htmlFor="additionalNotes" className="block text-sm font-medium text-gray-700 mb-1">
                      Additional Notes
                    </label>
                    <textarea
                      id="additionalNotes"
                      name="additionalNotes"
                      value={formData.additionalNotes}
                      onChange={handleInputChange}
                      rows={4}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                      placeholder="Any special requirements or additional information..."
                    />
                  </div>
                </div>
              </div>

              {/* Service Information */}
              <div className="mb-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h4 className="font-semibold text-blue-800 mb-2">GPS Installation Service Includes:</h4>
                <ul className="text-sm text-blue-700 space-y-1">
                  <li>• Professional GPS device installation</li>
                  <li>• Real-time tracking setup</li>
                  <li>• Mobile app configuration</li>
                  <li>• Training on how to use the system</li>
                  <li>• 1-year warranty on installation</li>
                  <li>• 24/7 technical support</li>
                </ul>
                <p className="text-sm text-blue-600 mt-3">
                  <strong>Pricing:</strong> Our team will provide a detailed quote based on your vehicle type and requirements.
                </p>
              </div>

              <div className="flex justify-end space-x-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => navigate('/owner/dashboard')}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? 'Submitting Request...' : 'Submit GPS Installation Request'}
                </Button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default GPSRequestPage;
