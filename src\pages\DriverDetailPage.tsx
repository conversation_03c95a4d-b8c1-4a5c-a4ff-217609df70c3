import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { MapPin, Calendar, Award, Star, ArrowLeft, Clock, MessageCircle, Phone } from 'lucide-react';
import MainLayout from '../components/layout/MainLayout';
import { useDrivers } from '../context/DriverContext';
import { useBookings } from '../context/BookingContext';
import { useAuth } from '../context/AuthContext';
import ChatBox from '../components/features/ChatBox';
import Button from '../components/ui/Button';
import Spinner from '../components/ui/Spinner';
import { formatCurrency, formatPrice } from '../utils/currency';
import CurrencyIcon from '../components/ui/CurrencyIcon';

const DriverDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { getDriverById, selectedDriver, isLoading, error } = useDrivers();
  const { createBooking } = useBookings();
  const { user, isAuthenticated } = useAuth();

  const [showChat, setShowChat] = useState(false);
  const [showContact, setShowContact] = useState(false);
  const [bookingHours, setBookingHours] = useState(1);
  const [bookingDate, setBookingDate] = useState<string>('');
  const [bookingTime, setBookingTime] = useState<string>('');
  const [isBooking, setIsBooking] = useState(false);
  const [bookingError, setBookingError] = useState<string | null>(null);
  const [bookingSuccess, setBookingSuccess] = useState(false);

  const handleBack = () => {
    navigate(-1);
  };

  useEffect(() => {
    window.scrollTo(0, 0);

    if (id) {
      getDriverById(id);
    }
  }, [id, getDriverById]);

  // Require authentication to view driver details
  if (!isAuthenticated) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <Button
            variant="outline"
            className="mb-4 flex items-center"
            onClick={handleBack}
          >
            <ArrowLeft size={18} className="mr-2" />
            Back to drivers
          </Button>

          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center">
            <h2 className="text-xl font-medium text-yellow-800 mb-2">Login Required</h2>
            <p className="text-yellow-700 mb-6">
              You must be logged in to view driver details and make bookings.
            </p>
            <div className="flex justify-center space-x-4">
              <Button onClick={() => navigate('/login')}>
                Log In
              </Button>
              <Button variant="outline" onClick={() => navigate('/signup')}>
                Sign Up
              </Button>
            </div>
          </div>
        </div>
      </MainLayout>
    );
  }

  const handleBookingSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!isAuthenticated || !user) {
      navigate('/login');
      return;
    }

    if (!selectedDriver) return;

    setIsBooking(true);
    setBookingError(null);

    try {
      // Create start and end times from the booking date and time
      const startDateTime = new Date(`${bookingDate}T${bookingTime}`);
      const endDateTime = new Date(startDateTime.getTime() + bookingHours * 60 * 60 * 1000);

      // Calculate total price
      const totalPrice = selectedDriver.pricePerHour * bookingHours;

      // Create booking
      await createBooking({
        userId: user.id,
        itemType: 'driver',
        itemId: selectedDriver.id,
        startTime: startDateTime.toISOString(),
        endTime: endDateTime.toISOString(),
        totalPrice,
        status: 'pending',
      });

      setBookingSuccess(true);
    } catch (err) {
      setBookingError('Failed to create booking. Please try again.');
      console.error(err);
    } finally {
      setIsBooking(false);
    }
  };

  if (isLoading) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="flex justify-center items-center h-64">
            <Spinner size="lg" />
          </div>
        </div>
      </MainLayout>
    );
  }

  if (error || !selectedDriver) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <Button
            variant="outline"
            className="mb-4 flex items-center"
            onClick={handleBack}
          >
            <ArrowLeft size={18} className="mr-2" />
            Back to drivers
          </Button>

          <div className="bg-error-50 border border-error-200 rounded-lg p-4 text-center">
            <h2 className="text-xl font-medium text-error-800 mb-2">Driver Not Found</h2>
            <p className="text-error-600">
              The driver you're looking for doesn't exist or has been removed.
            </p>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <Button
          variant="outline"
          className="mb-4 flex items-center"
          onClick={handleBack}
        >
          <ArrowLeft size={18} className="mr-2" />
          Back to drivers
        </Button>

        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="md:flex">
            <div className="md:w-2/3">
              <div className="relative h-80">
                <img
                  src={selectedDriver.profileImage}
                  alt={selectedDriver.name}
                  className="w-full h-full object-cover"
                />
                <div className="absolute bottom-4 right-4 bg-white px-3 py-1 rounded-full flex items-center shadow-md">
                  <Star size={18} className="text-yellow-500 mr-1" />
                  <span className="font-medium">{Number(selectedDriver.rating || 0).toFixed(1)}</span>
                  <span className="text-sm text-gray-500 ml-1">({selectedDriver.reviews || 0} reviews)</span>
                </div>
              </div>

              <div className="p-6">
                <h1 className="text-2xl font-bold text-gray-900 mb-2">
                  {selectedDriver.name}
                </h1>

                <div className="flex items-center mb-4">
                  <Award size={18} className="text-primary-600 mr-2" />
                  <span>{selectedDriver.experience} years of professional driving experience</span>
                </div>

                <div className="mb-4 space-y-3">
                  <div className="flex items-center text-gray-600">
                    <MapPin size={18} className="flex-shrink-0 mr-2" />
                    <span>{selectedDriver.location}</span>
                  </div>
                  <div className="flex items-center text-gray-600">
                    <Clock size={18} className="flex-shrink-0 mr-2" />
                    <span>{selectedDriver.availabilityNotes}</span>
                  </div>
                </div>

                <div className="mb-6">
                  <h2 className="text-lg font-semibold mb-2">Specialties</h2>
                  <div className="flex flex-wrap gap-2">
                    {selectedDriver.specialties.map((specialty, index) => (
                      <span
                        key={index}
                        className="inline-block bg-gray-100 rounded-full px-3 py-1 text-sm font-medium text-gray-700"
                      >
                        {specialty}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            <div className="md:w-1/3 p-6 border-l border-gray-200">
              <div className="mb-4 pb-4 border-b border-gray-200">
                <div className="text-2xl font-bold text-primary-600 mb-1">
                  {formatPrice(selectedDriver.pricePerHour, 'hour')}
                </div>
                <div className="text-sm text-gray-500">
                  Book this driver by the hour
                </div>
              </div>

              <div className="flex space-x-2 mb-4">
                <Button
                  variant="primary"
                  className="flex-1 flex items-center justify-center"
                  onClick={() => setShowChat(!showChat)}
                >
                  <MessageCircle size={18} className="mr-2" />
                  {showChat ? 'Hide Chat' : 'Chat with Driver'}
                </Button>

                {!showContact ? (
                  <Button
                    variant="outline"
                    className="flex-1 flex items-center justify-center"
                    onClick={() => setShowContact(true)}
                  >
                    <Phone size={18} className="mr-2" />
                    Show Contact
                  </Button>
                ) : (
                  <Button
                    variant="outline"
                    className="flex-1 flex items-center justify-center"
                    disabled
                  >
                    <Phone size={18} className="mr-2" />
                    {user?.isPhoneVerified ? '+****************' : 'Verify account first'}
                  </Button>
                )}
              </div>

              {showChat && (
                <div className="mb-4">
                  <ChatBox
                    recipientId={selectedDriver.userId}
                    recipientName={selectedDriver.name}
                    itemType="driver"
                    itemId={selectedDriver.id}
                  />
                </div>
              )}
            </div>
          </div>

          {/* Booking Form Section - Moved below driver details and chat */}
          <div className="mt-6 p-6 border-t border-gray-200">
            <h2 className="text-xl font-bold text-gray-900 mb-4">Book this Driver</h2>

            {bookingSuccess ? (
              <div className="bg-success-50 border border-success-200 rounded-lg p-4 text-center mb-4">
                <h3 className="text-lg font-medium text-success-800 mb-2">Booking Request Sent!</h3>
                <p className="text-success-600 mb-4">
                  Your booking request has been submitted. The driver will contact you soon to arrange payment and details.
                </p>
                <Button
                  onClick={() => navigate('/account')}
                  fullWidth
                >
                  View My Bookings
                </Button>
              </div>
            ) : (
              <form onSubmit={handleBookingSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label htmlFor="booking-date" className="block text-sm font-medium text-gray-700 mb-1">
                      Date
                    </label>
                    <input
                      type="date"
                      id="booking-date"
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      required
                      min={new Date().toISOString().split('T')[0]}
                      value={bookingDate}
                      onChange={(e) => setBookingDate(e.target.value)}
                    />
                  </div>

                  <div>
                    <label htmlFor="booking-time" className="block text-sm font-medium text-gray-700 mb-1">
                      Start Time
                    </label>
                    <input
                      type="time"
                      id="booking-time"
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      required
                      value={bookingTime}
                      onChange={(e) => setBookingTime(e.target.value)}
                    />
                  </div>

                  <div>
                    <label htmlFor="booking-hours" className="block text-sm font-medium text-gray-700 mb-1">
                      Hours
                    </label>
                    <select
                      id="booking-hours"
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      value={bookingHours}
                      onChange={(e) => setBookingHours(Number(e.target.value))}
                    >
                      {[1, 2, 3, 4, 5, 6, 7, 8].map((hours) => (
                        <option key={hours} value={hours}>
                          {hours} {hours === 1 ? 'hour' : 'hours'}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                <div className="pt-4 border-t border-gray-200">
                  <div className="flex justify-between mb-2">
                    <span>Rate per hour</span>
                    <span>{formatCurrency(selectedDriver.pricePerHour)}</span>
                  </div>
                  <div className="flex justify-between mb-2">
                    <span>Hours</span>
                    <span>{bookingHours}</span>
                  </div>
                  <div className="flex justify-between font-bold text-lg">
                    <span>Total</span>
                    <span>{formatCurrency(selectedDriver.pricePerHour * bookingHours)}</span>
                  </div>
                </div>

                {bookingError && (
                  <div className="bg-error-50 border border-error-200 rounded-lg p-3 text-error-700 text-sm">
                    {bookingError}
                  </div>
                )}

                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 text-yellow-700 text-sm mb-4">
                  <p><strong>Note:</strong> This is just a booking request. No payment is required on the platform.
                  You'll arrange payment directly with the driver.</p>
                </div>

                <Button
                  type="submit"
                  fullWidth
                  disabled={isBooking || !bookingDate || !bookingTime}
                >
                  {isBooking ? <Spinner size="sm" className="mr-2" /> : null}
                  Request Booking
                </Button>

                {!isAuthenticated && (
                  <div className="text-sm text-center text-gray-500">
                    You'll need to log in to complete your booking.
                  </div>
                )}
              </form>
            )}
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default DriverDetailPage;
