/**
 * Utility functions for currency formatting
 */

// Currency code for Rwandan Franc
export const CURRENCY_CODE = 'RWF';

// Currency symbol for Rwandan Franc (using the franc symbol)
export const CURRENCY_SYMBOL = '';

/**
 * Format a number as currency
 * @param amount - The amount to format
 * @param showSymbol - Whether to show the currency symbol
 * @returns Formatted currency string
 */
export const formatCurrency = (amount: number, showSymbol = true): string => {
  // Format with thousands separator and no decimal places (RWF doesn't use decimals)
  const formattedAmount = amount.toLocaleString('en-RW', {
    maximumFractionDigits: 0,
    minimumFractionDigits: 0,
  });

  // We don't add the symbol here because it's handled by the CurrencyIcon component
  return formattedAmount;
};

/**
 * Format a price per time period
 * @param amount - The amount to format
 * @param period - The time period (hour, day, etc.)
 * @returns Formatted price string
 */
export const formatPrice = (amount: number, period: string): string => {
  return `${formatCurrency(amount)}/${period.toLowerCase()}`;
};
