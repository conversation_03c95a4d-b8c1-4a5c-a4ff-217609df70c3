import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { MapPin, Calendar, Info, ArrowLeft, MessageCircle, Clock } from 'lucide-react';
import MainLayout from '../components/layout/MainLayout';
import { useCars } from '../context/CarContext';
import { useBookings } from '../context/BookingContext';
import { useAuth } from '../context/AuthContext';
import ImageCarousel from '../components/features/ImageCarousel';
import PriceCalculator from '../components/features/PriceCalculator';
import ContactOwnerButton from '../components/features/ContactOwnerButton';
import ChatBox from '../components/features/ChatBox';
import Button from '../components/ui/Button';
import Spinner from '../components/ui/Spinner';
import { formatCurrency } from '../utils/currency';
import CurrencyIcon from '../components/ui/CurrencyIcon';

const CarDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { getCarById, selectedCar, isLoading, error } = useCars();
  const { createBooking } = useBookings();
  const { user, isAuthenticated } = useAuth();

  const [showChat, setShowChat] = useState(false);
  const [bookingHours, setBookingHours] = useState(1);
  const [bookingDate, setBookingDate] = useState<string>('');
  const [bookingTime, setBookingTime] = useState<string>('');
  const [isBooking, setIsBooking] = useState(false);
  const [bookingError, setBookingError] = useState<string | null>(null);
  const [bookingSuccess, setBookingSuccess] = useState(false);

  const handleBack = () => {
    navigate(-1);
  };

  useEffect(() => {
    window.scrollTo(0, 0);

    if (id) {
      getCarById(id);
    }
  }, [id, getCarById]);

  // Require authentication to view car details
  if (!isAuthenticated) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <Button
            variant="outline"
            className="mb-4 flex items-center"
            onClick={handleBack}
          >
            <ArrowLeft size={18} className="mr-2" />
            Back to listings
          </Button>

          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center">
            <h2 className="text-xl font-medium text-yellow-800 mb-2">Login Required</h2>
            <p className="text-yellow-700 mb-6">
              You must be logged in to view car details and make bookings.
            </p>
            <div className="flex justify-center space-x-4">
              <Button onClick={() => navigate('/login')}>
                Log In
              </Button>
              <Button variant="outline" onClick={() => navigate('/signup')}>
                Sign Up
              </Button>
            </div>
          </div>
        </div>
      </MainLayout>
    );
  }

  if (isLoading) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="flex justify-center items-center h-64">
            <Spinner size="lg" />
          </div>
        </div>
      </MainLayout>
    );
  }

  if (error || !selectedCar) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <Button
            variant="outline"
            className="mb-4 flex items-center"
            onClick={handleBack}
          >
            <ArrowLeft size={18} className="mr-2" />
            Back to listings
          </Button>

          <div className="bg-error-50 border border-error-200 rounded-lg p-4 text-center">
            <h2 className="text-xl font-medium text-error-800 mb-2">Car Not Found</h2>
            <p className="text-error-600">
              The car you're looking for doesn't exist or has been removed.
            </p>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <Button
          variant="outline"
          className="mb-4 flex items-center"
          onClick={handleBack}
        >
          <ArrowLeft size={18} className="mr-2" />
          Back to listings
        </Button>

        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="md:flex">
            <div className="md:w-2/3">
              <ImageCarousel
                images={selectedCar.images}
                alt={`${selectedCar.year} ${selectedCar.make} ${selectedCar.model}`}
              />
            </div>

            <div className="md:w-1/3 p-6 border-l border-gray-200">
              <h1 className="text-2xl font-bold text-gray-900 mb-2">
                {selectedCar.year} {selectedCar.make} {selectedCar.model}
              </h1>

              <div className="flex items-center text-gray-600 mb-4">
                <MapPin size={18} className="flex-shrink-0 mr-2" />
                <span>{selectedCar.location}</span>
              </div>

              <div className="mb-4 pb-4 border-b border-gray-200">
                <div className="flex items-center text-gray-600 mb-2">
                  <Calendar size={18} className="mr-2 flex-shrink-0" />
                  <span>Availability: {selectedCar.availabilityNotes}</span>
                </div>
              </div>

              <PriceCalculator hourlyRate={selectedCar.pricePerHour} />

              <div className="flex space-x-2 mt-4">
                <ContactOwnerButton ownerId={selectedCar.ownerId} />
              </div>
            </div>
          </div>

          <div className="p-6 border-t border-gray-200">
            <h2 className="text-xl font-bold text-gray-900 mb-4">About This Car</h2>
            <p className="text-gray-700 mb-6">{selectedCar.description}</p>

            <h3 className="text-lg font-medium text-gray-900 mb-3">Features</h3>
            <ul className="grid grid-cols-1 md:grid-cols-2 gap-2 mb-6">
              {selectedCar.features.map((feature, index) => (
                <li key={index} className="flex items-center text-gray-700">
                  <Info size={16} className="text-primary-600 mr-2 flex-shrink-0" />
                  {feature}
                </li>
              ))}
            </ul>

            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
              <h3 className="text-lg font-medium text-yellow-800 mb-2">Important Information</h3>
              <p className="text-yellow-700">
                Park & Rent only facilitates the connection between car owners and renters.
                All arrangements, payments, and transactions are handled directly between users.
                We recommend chatting with the owner first, verifying insurance coverage, and discussing details thoroughly before booking.
              </p>
            </div>

            <div className="mb-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">Chat with Owner</h3>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowChat(!showChat)}
                >
                  {showChat ? 'Hide Chat' : 'Show Chat'}
                </Button>
              </div>

              {showChat ? (
                <ChatBox
                  recipientId={selectedCar.ownerId}
                  recipientName="Car Owner"
                  itemType="car"
                  itemId={selectedCar.id}
                />
              ) : (
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 text-center">
                  <MessageCircle size={32} className="mx-auto text-gray-400 mb-2" />
                  <p className="text-gray-600">
                    Chat with the car owner to ask questions or discuss details before booking.
                  </p>
                </div>
              )}
            </div>

            {/* Booking Form Section - Moved below chat */}
            <div className="mt-6 pt-6 border-t border-gray-200">
              <h2 className="text-xl font-bold text-gray-900 mb-4">Book this Car</h2>

              {!bookingSuccess ? (
                <form onSubmit={(e) => {
                  e.preventDefault();
                  if (!isAuthenticated || !user) {
                    navigate('/login');
                    return;
                  }

                  setIsBooking(true);
                  setBookingError(null);

                  try {
                    // Create start and end times from the booking date and time
                    const startDateTime = new Date(`${bookingDate}T${bookingTime}`);
                    const endDateTime = new Date(startDateTime.getTime() + bookingHours * 60 * 60 * 1000);

                    // Calculate total price
                    const totalPrice = selectedCar.pricePerHour * bookingHours;

                    // Create booking
                    createBooking({
                      userId: user.id,
                      itemType: 'car',
                      itemId: selectedCar.id,
                      startTime: startDateTime.toISOString(),
                      endTime: endDateTime.toISOString(),
                      totalPrice,
                      status: 'pending',
                    }).then(() => {
                      setBookingSuccess(true);
                      setIsBooking(false);
                    });
                  } catch (err) {
                    setBookingError('Failed to create booking. Please try again.');
                    console.error(err);
                    setIsBooking(false);
                  }
                }} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label htmlFor="booking-date" className="block text-sm font-medium text-gray-700 mb-1">
                        Date
                      </label>
                      <input
                        type="date"
                        id="booking-date"
                        className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                        required
                        min={new Date().toISOString().split('T')[0]}
                        value={bookingDate}
                        onChange={(e) => setBookingDate(e.target.value)}
                      />
                    </div>

                    <div>
                      <label htmlFor="booking-time" className="block text-sm font-medium text-gray-700 mb-1">
                        Start Time
                      </label>
                      <input
                        type="time"
                        id="booking-time"
                        className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                        required
                        value={bookingTime}
                        onChange={(e) => setBookingTime(e.target.value)}
                      />
                    </div>

                    <div>
                      <label htmlFor="booking-hours" className="block text-sm font-medium text-gray-700 mb-1">
                        Hours
                      </label>
                      <select
                        id="booking-hours"
                        className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                        value={bookingHours}
                        onChange={(e) => setBookingHours(Number(e.target.value))}
                      >
                        {[1, 2, 3, 4, 5, 6, 7, 8].map((hours) => (
                          <option key={hours} value={hours}>
                            {hours} {hours === 1 ? 'hour' : 'hours'}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>

                  <div className="pt-4 border-t border-gray-200">
                    <div className="flex justify-between mb-2">
                      <span>Rate per hour</span>
                      <span>{formatCurrency(selectedCar.pricePerHour)}</span>
                    </div>
                    <div className="flex justify-between mb-2">
                      <span>Hours</span>
                      <span>{bookingHours}</span>
                    </div>
                    <div className="flex justify-between font-bold text-lg">
                      <span>Total</span>
                      <span>{formatCurrency(selectedCar.pricePerHour * bookingHours)}</span>
                    </div>
                  </div>

                  {bookingError && (
                    <div className="bg-error-50 border border-error-200 rounded-lg p-3 text-error-700 text-sm">
                      {bookingError}
                    </div>
                  )}

                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 text-yellow-700 text-sm mb-4">
                    <p><strong>Note:</strong> This is just a booking request. No payment is required on the platform.
                    You'll arrange payment directly with the car owner.</p>
                  </div>

                  <Button
                    type="submit"
                    fullWidth
                    disabled={isBooking || !bookingDate || !bookingTime}
                  >
                    {isBooking ? <Spinner size="sm" className="mr-2" /> : null}
                    Request Booking
                  </Button>

                  {!isAuthenticated && (
                    <div className="text-sm text-center text-gray-500">
                      You'll need to log in to complete your booking.
                    </div>
                  )}
                </form>
              ) : (
                <div className="bg-success-50 border border-success-200 rounded-lg p-4 text-center">
                  <h3 className="text-lg font-medium text-success-800 mb-2">Booking Request Sent!</h3>
                  <p className="text-success-600 mb-4">
                    Your booking request has been submitted. The car owner will contact you soon to arrange payment and details.
                  </p>
                  <Button
                    onClick={() => navigate('/account')}
                    variant="primary"
                  >
                    View My Bookings
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default CarDetailPage;