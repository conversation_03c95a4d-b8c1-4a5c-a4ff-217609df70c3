import React, { useState } from 'react';
import { Search, Filter } from 'lucide-react';
import Button from '../ui/Button';

interface CarFiltersProps {
  onSearch: (query: string) => void;
}

const CarFilters: React.FC<CarFiltersProps> = ({ onSearch }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [isFiltersOpen, setIsFiltersOpen] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch(searchQuery);
  };

  const toggleFilters = () => {
    setIsFiltersOpen(!isFiltersOpen);
  };

  return (
    <div className="mb-6">
      <form onSubmit={handleSubmit} className="flex items-center mb-4">
        <div className="relative flex-grow">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search size={18} className="text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search cars by make, model, or location..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          />
        </div>
        <Button type="submit" className="rounded-l-none">
          Search
        </Button>
        <Button
          type="button"
          variant="outline"
          className="ml-2 flex items-center md:hidden"
          onClick={toggleFilters}
        >
          <Filter size={18} className="mr-1" />
          Filters
        </Button>
      </form>

      <div className={`md:block ${isFiltersOpen ? 'block' : 'hidden'} bg-white p-4 rounded-lg shadow-sm mb-4 border border-gray-200`}>
        <h3 className="font-medium text-gray-900 mb-3">Filter Cars</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label htmlFor="price-range" className="block text-sm font-medium text-gray-700 mb-1">
              Price Range (per hour)
            </label>
            <select
              id="price-range"
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="">Any Price</option>
              <option value="0-5000">₣ 0 - 5,000</option>
              <option value="5000-10000">₣ 5,000 - 10,000</option>
              <option value="10000-20000">₣ 10,000 - 20,000</option>
              <option value="20000+">₣ 20,000+</option>
            </select>
          </div>

          <div>
            <label htmlFor="car-type" className="block text-sm font-medium text-gray-700 mb-1">
              Car Type
            </label>
            <select
              id="car-type"
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="">All Types</option>
              <option value="sedan">Sedan</option>
              <option value="suv">SUV</option>
              <option value="truck">Truck</option>
              <option value="luxury">Luxury</option>
              <option value="electric">Electric</option>
            </select>
          </div>

          <div>
            <label htmlFor="availability" className="block text-sm font-medium text-gray-700 mb-1">
              Availability
            </label>
            <select
              id="availability"
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="">Any Time</option>
              <option value="weekdays">Weekdays</option>
              <option value="weekends">Weekends</option>
              <option value="all-day">All Day</option>
            </select>
          </div>
        </div>

        <div className="mt-4 flex justify-end">
          <Button
            type="button"
            variant="outline"
            size="sm"
            className="mr-2"
            onClick={() => {
              // Reset filters logic would go here
              setIsFiltersOpen(false);
            }}
          >
            Reset
          </Button>
          <Button
            type="button"
            size="sm"
            onClick={() => {
              // Apply filters logic would go here
              setIsFiltersOpen(false);
            }}
          >
            Apply Filters
          </Button>
        </div>
      </div>
    </div>
  );
};

export default CarFilters;