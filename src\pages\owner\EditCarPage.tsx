import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Car, Upload, X, Plus, MapPin, DollarSign, Info, AlertCircle, ArrowLeft } from 'lucide-react';
import MainLayout from '../../components/layout/MainLayout';
import Button from '../../components/ui/Button';
import { useCars } from '../../context/CarContext';
import { useAuth } from '../../context/AuthContext';

const EditCarPage: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const { cars, updateCar, getCarById } = useCars();
  const { user } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [loading, setLoading] = useState(true);

  // Find the car to edit
  const car = cars.find(c => c.id === id);

  // Form state
  const [formData, setFormData] = useState({
    make: '',
    model: '',
    year: new Date().getFullYear(),
    description: '',
    location: '',
    pricePerHour: 0,
    availabilityNotes: '',
    isActive: true,
  });

  // Features state
  const [features, setFeatures] = useState<string[]>([]);
  const [newFeature, setNewFeature] = useState('');

  // Images state
  const [images, setImages] = useState<string[]>([]);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);

  useEffect(() => {
    if (!id) {
      navigate('/owner/dashboard');
      return;
    }

    if (car) {
      // Populate form with existing car data
      setFormData({
        make: car.make,
        model: car.model,
        year: car.year,
        description: car.description,
        location: car.location,
        pricePerHour: car.pricePerHour,
        availabilityNotes: car.availabilityNotes || '',
        isActive: car.isActive,
      });
      setFeatures(car.features || []);
      setImages(car.images || []);
      setLoading(false);
    } else {
      // Try to fetch the car if not in local state
      getCarById(id).then(() => {
        setLoading(false);
      }).catch(() => {
        setError('Car not found');
        setLoading(false);
      });
    }
  }, [id, car, navigate, getCarById]);

  // Check if user owns this car
  useEffect(() => {
    if (car && user && car.ownerId !== user.id) {
      setError('You can only edit your own cars');
    }
  }, [car, user]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: name === 'year' || name === 'pricePerHour' ? Number(value) : value,
    });
  };

  const handleAddFeature = () => {
    if (newFeature.trim() && !features.includes(newFeature.trim())) {
      setFeatures([...features, newFeature.trim()]);
      setNewFeature('');
    }
  };

  const handleRemoveFeature = (index: number) => {
    setFeatures(features.filter((_, i) => i !== index));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);

    files.forEach(file => {
      if (file.size > 5 * 1024 * 1024) { // 5MB limit
        setError('Each image must be less than 5MB');
        return;
      }

      const reader = new FileReader();
      reader.onload = () => {
        const imageUrl = reader.result as string;
        setImages(prev => [...prev, imageUrl]);
        setSelectedFiles(prev => [...prev, file]);
      };
      reader.readAsDataURL(file);
    });
  };

  const handleRemoveImage = (imageUrl: string, index: number) => {
    // Remove the image URL
    setImages(images.filter(img => img !== imageUrl));

    // Remove the corresponding file if it's a new upload
    if (index < selectedFiles.length) {
      setSelectedFiles(prev => {
        const newFiles = [...prev];
        newFiles.splice(index, 1);
        return newFiles;
      });
    }

    // Revoke the object URL to avoid memory leaks (only for new uploads)
    if (imageUrl.startsWith('data:')) {
      URL.revokeObjectURL(imageUrl);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    if (!formData.make || !formData.model || !formData.description || !formData.location || formData.pricePerHour <= 0) {
      setError('Please fill in all required fields');
      return;
    }

    if (images.length === 0) {
      setError('Please keep at least one image');
      return;
    }

    if (features.length === 0) {
      setError('Please add at least one feature');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      if (!user || !car) {
        throw new Error('Invalid user or car data');
      }

      // Update the car
      await updateCar(car.id, {
        make: formData.make,
        model: formData.model,
        year: formData.year,
        description: formData.description,
        features,
        location: formData.location,
        pricePerHour: formData.pricePerHour,
        availabilityNotes: formData.availabilityNotes,
        isActive: formData.isActive,
        // Note: Image updates would need special handling in a real app
        images: images,
      });

      setSuccess(true);

      // Clean up object URLs to avoid memory leaks
      images.forEach(url => {
        if (url.startsWith('blob:')) {
          URL.revokeObjectURL(url);
        }
      });

      // Redirect after a short delay
      setTimeout(() => {
        navigate('/owner/dashboard');
      }, 2000);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update car');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Loading car details...</p>
            </div>
          </div>
        </div>
      </MainLayout>
    );
  }

  if (error && !car) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-3xl mx-auto">
            <div className="flex items-center mb-6">
              <button
                onClick={() => navigate('/owner/dashboard')}
                className="text-primary-600 hover:text-primary-800 mr-4"
              >
                ← Back to Dashboard
              </button>
              <h1 className="text-3xl font-bold text-gray-900">Edit Car</h1>
            </div>
            <div className="bg-error-50 border border-error-200 rounded-lg p-4 text-center">
              <h2 className="text-xl font-medium text-error-800 mb-2">Error</h2>
              <p className="text-error-600">{error}</p>
            </div>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-3xl mx-auto">
          <div className="flex items-center mb-6">
            <button
              onClick={() => navigate('/owner/dashboard')}
              className="text-primary-600 hover:text-primary-800 mr-4 flex items-center"
            >
              <ArrowLeft size={20} className="mr-1" />
              Back to Dashboard
            </button>
            <h1 className="text-3xl font-bold text-gray-900">Edit Car</h1>
          </div>

          {success ? (
            <div className="bg-success-50 border border-success-200 rounded-lg p-8 text-center">
              <div className="w-16 h-16 bg-success-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Car size={32} className="text-success-600" />
              </div>
              <h2 className="text-2xl font-bold text-success-800 mb-4">Car Updated Successfully!</h2>
              <p className="text-success-700 mb-6">
                Your car listing has been updated and is now live on the platform.
              </p>
              <Button onClick={() => navigate('/owner/dashboard')}>
                Return to Dashboard
              </Button>
            </div>
          ) : (
            <form onSubmit={handleSubmit}>
              {error && (
                <div className="mb-6 bg-error-50 border border-error-200 rounded-lg p-4 flex items-start">
                  <AlertCircle size={20} className="text-error-600 mr-2 flex-shrink-0 mt-0.5" />
                  <div className="text-error-700">{error}</div>
                </div>
              )}

              {/* Car Details */}
              <div className="bg-white rounded-lg shadow-md overflow-hidden mb-6">
                <div className="p-6">
                  <h2 className="text-xl font-semibold mb-4">Car Details</h2>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                      <label htmlFor="make" className="block text-sm font-medium text-gray-700 mb-1">
                        Make <span className="text-error-600">*</span>
                      </label>
                      <input
                        type="text"
                        id="make"
                        name="make"
                        value={formData.make}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="e.g., Toyota"
                        required
                      />
                    </div>

                    <div>
                      <label htmlFor="model" className="block text-sm font-medium text-gray-700 mb-1">
                        Model <span className="text-error-600">*</span>
                      </label>
                      <input
                        type="text"
                        id="model"
                        name="model"
                        value={formData.model}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="e.g., Camry"
                        required
                      />
                    </div>
                  </div>

                  <div className="mb-6">
                    <label htmlFor="year" className="block text-sm font-medium text-gray-700 mb-1">
                      Year <span className="text-error-600">*</span>
                    </label>
                    <select
                      id="year"
                      name="year"
                      value={formData.year}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                      required
                    >
                      {Array.from({ length: 30 }, (_, i) => new Date().getFullYear() - i).map(year => (
                        <option key={year} value={year}>{year}</option>
                      ))}
                    </select>
                  </div>

                  <div className="mb-6">
                    <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                      Description <span className="text-error-600">*</span>
                    </label>
                    <textarea
                      id="description"
                      name="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      rows={4}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                      placeholder="Describe your car, its condition, and any special features..."
                      required
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                      <label htmlFor="location" className="block text-sm font-medium text-gray-700 mb-1">
                        Location <span className="text-error-600">*</span>
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <MapPin size={18} className="text-gray-400" />
                        </div>
                        <input
                          type="text"
                          id="location"
                          name="location"
                          value={formData.location}
                          onChange={handleInputChange}
                          className="w-full pl-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                          placeholder="City, Country"
                          required
                        />
                      </div>
                    </div>

                    <div>
                      <label htmlFor="pricePerHour" className="block text-sm font-medium text-gray-700 mb-1">
                        Price per Hour (RWF) <span className="text-error-600">*</span>
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <DollarSign size={18} className="text-gray-400" />
                        </div>
                        <input
                          type="number"
                          id="pricePerHour"
                          name="pricePerHour"
                          value={formData.pricePerHour}
                          onChange={handleInputChange}
                          min="0"
                          step="1000"
                          className="w-full pl-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                          required
                        />
                      </div>
                    </div>
                  </div>

                  <div className="mb-6">
                    <label htmlFor="availabilityNotes" className="block text-sm font-medium text-gray-700 mb-1">
                      Availability Notes
                    </label>
                    <input
                      type="text"
                      id="availabilityNotes"
                      name="availabilityNotes"
                      value={formData.availabilityNotes}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                      placeholder="e.g., Available weekdays 9 AM - 6 PM"
                    />
                  </div>

                  <div className="mb-6">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={formData.isActive}
                        onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                        className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                      />
                      <span className="ml-2 text-sm text-gray-700">
                        Active (visible on the website)
                      </span>
                    </label>
                  </div>
                </div>
              </div>

              {/* Features */}
              <div className="bg-white rounded-lg shadow-md overflow-hidden mb-6">
                <div className="p-6">
                  <h2 className="text-xl font-semibold mb-4">Features</h2>

                  <div className="flex mb-4">
                    <input
                      type="text"
                      value={newFeature}
                      onChange={(e) => setNewFeature(e.target.value)}
                      className="flex-grow px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                      placeholder="Add a feature (e.g., Air Conditioning)"
                    />
                    <Button
                      type="button"
                      onClick={handleAddFeature}
                      className="rounded-l-none"
                    >
                      <Plus size={18} className="mr-1" /> Add
                    </Button>
                  </div>

                  {features.length === 0 ? (
                    <div className="text-gray-500 italic mb-4">
                      No features added yet. Please add at least one feature.
                    </div>
                  ) : (
                    <div className="flex flex-wrap gap-2 mb-4">
                      {features.map((feature, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-primary-100 text-primary-800"
                        >
                          {feature}
                          <button
                            type="button"
                            onClick={() => handleRemoveFeature(index)}
                            className="ml-2 text-primary-600 hover:text-primary-800"
                          >
                            <X size={14} />
                          </button>
                        </span>
                      ))}
                    </div>
                  )}

                  <div className="text-sm text-gray-500 flex items-start">
                    <Info size={16} className="mr-1 mt-0.5 flex-shrink-0" />
                    <span>
                      Add features that make your car attractive to renters (e.g., GPS, Bluetooth, etc.)
                    </span>
                  </div>
                </div>
              </div>

              {/* Images */}
              <div className="bg-white rounded-lg shadow-md overflow-hidden mb-6">
                <div className="p-6">
                  <h2 className="text-xl font-semibold mb-4">Images</h2>

                  <div className="mb-4">
                    <label
                      htmlFor="car-images"
                      className="block w-full cursor-pointer text-center py-8 px-4 border-2 border-dashed border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      <Upload size={36} className="mx-auto mb-2 text-gray-400" />
                      <span className="block text-sm font-medium text-gray-700 mb-1">
                        Click to add more images
                      </span>
                      <span className="text-xs text-gray-500">
                        JPG, PNG or WEBP (max. 5MB each)
                      </span>
                      <input
                        id="car-images"
                        type="file"
                        multiple
                        accept="image/jpeg,image/png,image/webp"
                        onChange={handleFileChange}
                        className="hidden"
                      />
                    </label>
                  </div>

                  {images.length === 0 ? (
                    <div className="text-gray-500 italic mb-4">
                      No images available. Please add at least one image.
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mb-4">
                      {images.map((image, index) => (
                        <div key={index} className="relative group">
                          <img
                            src={image}
                            alt={`Car image ${index + 1}`}
                            className="w-full h-40 object-cover rounded-md"
                            onError={(e) => {
                              const img = e.target as HTMLImageElement;
                              img.style.display = 'none';
                              const parent = img.parentElement;
                              if (parent && !parent.querySelector('.image-fallback')) {
                                const fallback = document.createElement('div');
                                fallback.className = 'image-fallback w-full h-40 bg-gray-200 flex items-center justify-center text-gray-500 text-sm rounded-md';
                                fallback.textContent = 'Image Error';
                                parent.appendChild(fallback);
                              }
                            }}
                          />
                          <button
                            type="button"
                            onClick={() => handleRemoveImage(image, index)}
                            className="absolute top-2 right-2 bg-white rounded-full p-1 shadow-md text-gray-700 hover:text-error-600"
                          >
                            <X size={16} />
                          </button>
                        </div>
                      ))}
                    </div>
                  )}

                  <div className="text-sm text-gray-500 flex items-start">
                    <Info size={16} className="mr-1 mt-0.5 flex-shrink-0" />
                    <span>
                      Current images will be kept. New images will be added to your listing.
                    </span>
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => navigate('/owner/dashboard')}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? 'Updating Car...' : 'Update Car'}
                </Button>
              </div>
            </form>
          )}
        </div>
      </div>
    </MainLayout>
  );
};

export default EditCarPage;
