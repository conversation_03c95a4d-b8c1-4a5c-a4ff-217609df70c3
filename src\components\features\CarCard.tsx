import React from 'react';
import { MapPin, Clock } from 'lucide-react';
import { Link } from 'react-router-dom';
import { Car } from '../../types';
import Card from '../ui/Card';
import Button from '../ui/Button';
import { formatPrice } from '../../utils/currency';
import CurrencyIcon from '../ui/CurrencyIcon';

interface CarCardProps {
  car: Car;
}

const CarCard: React.FC<CarCardProps> = ({ car }) => {
  return (
    <Card className="h-full flex flex-col">
      <Card.Image src={car.images[0]} alt={`${car.make} ${car.model}`} />
      <Card.Content className="flex-grow">
        <Card.Title>
          {car.year} {car.make} {car.model}
        </Card.Title>
        <div className="mb-4 space-y-2">
          <div className="flex items-center text-gray-600">
            <MapPin size={16} className="mr-1 flex-shrink-0" />
            <span className="text-sm truncate">{car.location}</span>
          </div>
          <div className="flex items-center text-gray-600">
            <Clock size={16} className="mr-1 flex-shrink-0" />
            <span className="text-sm truncate">{car.availabilityNotes}</span>
          </div>
          <div className="flex items-center font-medium text-primary-700">
            <CurrencyIcon size={16} className="mr-1 flex-shrink-0" />
            <span>{formatPrice(car.pricePerHour, 'hour')}</span>
          </div>
        </div>
        <div className="text-sm text-gray-600 line-clamp-2 mb-4">
          {car.description}
        </div>
      </Card.Content>
      <Card.Footer className="mt-auto">
        <Link to={`/cars/${car.id}`} className="w-full">
          <Button variant="primary" fullWidth>
            Book Now
          </Button>
        </Link>
      </Card.Footer>
    </Card>
  );
};

export default CarCard;