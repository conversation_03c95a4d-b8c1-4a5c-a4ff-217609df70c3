import React, { useEffect } from 'react';
import MainLayout from '../components/layout/MainLayout';
import { useCars } from '../context/CarContext';
import CarCard from '../components/features/CarCard';
import CarFilters from '../components/features/CarFilters';
import Spinner from '../components/ui/Spinner';
import { Car } from 'lucide-react';

const CarsListPage: React.FC = () => {
  const { filteredCars, isLoading, error, fetchCars, filterCars } = useCars();
  
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);
  
  const handleSearch = (query: string) => {
    filterCars(query);
  };
  
  if (isLoading) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="flex justify-center items-center h-64">
            <Spinner size="lg" />
          </div>
        </div>
      </MainLayout>
    );
  }
  
  if (error) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="bg-error-50 border border-error-200 rounded-lg p-4 text-center">
            <h2 className="text-xl font-medium text-error-800 mb-2">Error Loading Cars</h2>
            <p className="text-error-600">{error}</p>
          </div>
        </div>
      </MainLayout>
    );
  }
  
  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Available Cars</h1>
          <p className="text-gray-600">
            Browse our selection of available cars for rent from local owners.
          </p>
        </div>
        
        <CarFilters onSearch={handleSearch} />
        
        {filteredCars.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredCars.map((car) => (
              <CarCard key={car.id} car={car} />
            ))}
          </div>
        ) : (
          <div className="text-center py-16 bg-white rounded-lg shadow-sm border border-gray-200">
            <Car size={48} className="mx-auto text-gray-400 mb-4" />
            <h3 className="text-xl font-medium text-gray-900 mb-2">No cars match your search</h3>
            <p className="text-gray-600">
              Try adjusting your filters or search term to find available cars.
            </p>
          </div>
        )}
      </div>
    </MainLayout>
  );
};

export default CarsListPage;