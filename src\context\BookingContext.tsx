import React, { createContext, useContext, useState, useEffect } from 'react';
import { Booking } from '../types';
import { mockBookings } from '../data/mockData';

interface BookingContextType {
  bookings: Booking[];
  userBookings: Booking[];
  isLoading: boolean;
  error: string | null;
  fetchBookings: () => Promise<void>;
  getBookingById: (id: string) => Promise<Booking | null>;
  createBooking: (booking: Omit<Booking, 'id' | 'createdAt'>) => Promise<Booking>;
  updateBookingStatus: (id: string, status: Booking['status']) => Promise<Booking>;
  cancelBooking: (id: string) => Promise<Booking>;
  getUserBookings: (userId: string) => Booking[];
}

const BookingContext = createContext<BookingContextType | undefined>(undefined);

export const useBookings = () => {
  const context = useContext(BookingContext);
  if (!context) {
    throw new Error('useBookings must be used within a BookingProvider');
  }
  return context;
};

export const BookingProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [userBookings, setUserBookings] = useState<Booking[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchBookings = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Simulate API call with mock data
      // In a real app, this would be an API request
      setBookings(mockBookings);
    } catch (err) {
      setError('Failed to fetch bookings');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  const getBookingById = async (id: string): Promise<Booking | null> => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Find booking by ID
      const booking = bookings.find(booking => booking.id === id) || null;
      return booking;
    } catch (err) {
      setError('Failed to fetch booking');
      console.error(err);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  const createBooking = async (bookingData: Omit<Booking, 'id' | 'createdAt'>): Promise<Booking> => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Create new booking
      const newBooking: Booking = {
        ...bookingData,
        id: `booking-${Date.now()}`,
        createdAt: new Date().toISOString(),
      };
      
      setBookings(prevBookings => [...prevBookings, newBooking]);
      
      // Update user bookings if this is for the current user
      setUserBookings(prevUserBookings => {
        if (prevUserBookings.some(booking => booking.userId === bookingData.userId)) {
          return [...prevUserBookings, newBooking];
        }
        return prevUserBookings;
      });
      
      return newBooking;
    } catch (err) {
      setError('Failed to create booking');
      console.error(err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const updateBookingStatus = async (id: string, status: Booking['status']): Promise<Booking> => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Find and update booking
      const bookingIndex = bookings.findIndex(booking => booking.id === id);
      
      if (bookingIndex === -1) {
        throw new Error('Booking not found');
      }
      
      const updatedBooking = {
        ...bookings[bookingIndex],
        status,
      };
      
      const updatedBookings = [...bookings];
      updatedBookings[bookingIndex] = updatedBooking;
      
      setBookings(updatedBookings);
      
      // Update user bookings if necessary
      setUserBookings(prevUserBookings => {
        const userBookingIndex = prevUserBookings.findIndex(booking => booking.id === id);
        if (userBookingIndex !== -1) {
          const updatedUserBookings = [...prevUserBookings];
          updatedUserBookings[userBookingIndex] = updatedBooking;
          return updatedUserBookings;
        }
        return prevUserBookings;
      });
      
      return updatedBooking;
    } catch (err) {
      setError('Failed to update booking status');
      console.error(err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const cancelBooking = async (id: string): Promise<Booking> => {
    return updateBookingStatus(id, 'cancelled');
  };

  const getUserBookings = (userId: string): Booking[] => {
    return bookings.filter(booking => booking.userId === userId);
  };

  useEffect(() => {
    fetchBookings();
  }, []);

  const value = {
    bookings,
    userBookings,
    isLoading,
    error,
    fetchBookings,
    getBookingById,
    createBooking,
    updateBookingStatus,
    cancelBooking,
    getUserBookings,
  };

  return <BookingContext.Provider value={value}>{children}</BookingContext.Provider>;
};

export default BookingContext;
