<?php

use App\Http\Controllers\API\AdminController;
use App\Http\Controllers\API\AuthController;
use App\Http\Controllers\API\BookingController;
use App\Http\Controllers\API\CarController;
use App\Http\Controllers\API\ChatController;
use App\Http\Controllers\API\DriverController;
use App\Http\Controllers\API\MessageController;
use App\Http\Controllers\API\UserController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public routes
Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);

// Public car routes (for browsing)
Route::get('/cars', [CarController::class, 'index']);
Route::get('/cars/{id}', [CarController::class, 'show']);

// Protected routes
Route::middleware('auth:sanctum')->group(function () {
    // Auth routes
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::get('/user', [AuthController::class, 'user']);

    // User routes
    Route::apiResource('users', UserController::class);
    Route::post('/users/update-password', [UserController::class, 'updatePassword']);

    // Car routes (protected operations)
    Route::post('/cars', [CarController::class, 'store']);
    Route::put('/cars/{id}', [CarController::class, 'update']);
    Route::delete('/cars/{id}', [CarController::class, 'destroy']);
    Route::get('/my-cars', [CarController::class, 'myCars']);
    Route::post('/cars/{id}/toggle-active', [CarController::class, 'toggleActive']);

    // Driver routes
    Route::apiResource('drivers', DriverController::class);
    Route::get('/my-driver-profile', [DriverController::class, 'myProfile']);
    Route::post('/drivers/{id}/toggle-availability', [DriverController::class, 'toggleAvailability']);
    Route::post('/drivers/{id}/verify-license', [DriverController::class, 'verifyLicense']);

    // Booking routes
    Route::apiResource('bookings', BookingController::class);
    Route::get('/my-bookings', [BookingController::class, 'myBookings']);
    Route::post('/bookings/{id}/update-status', [BookingController::class, 'updateStatus']);

    // Chat routes
    Route::apiResource('chats', ChatController::class);
    Route::get('/my-chats', [ChatController::class, 'myChats']);
    Route::get('/unread-messages-count', [ChatController::class, 'unreadCount']);

    // Message routes
    Route::apiResource('messages', MessageController::class);
    Route::post('/messages/mark-as-read', [MessageController::class, 'markAsRead']);

    // Admin routes
    Route::prefix('admin')->group(function () {
        Route::get('/dashboard', [AdminController::class, 'dashboard']);
        Route::get('/users', [AdminController::class, 'users']);
        Route::get('/cars', [AdminController::class, 'cars']);
        Route::get('/drivers', [AdminController::class, 'drivers']);
        Route::get('/bookings', [AdminController::class, 'bookings']);
        Route::post('/drivers/{id}/verify-license', [AdminController::class, 'verifyDriverLicense']);
        Route::post('/users/{id}/update-role', [AdminController::class, 'updateUserRole']);
        Route::get('/revenue', [AdminController::class, 'revenue']);
    });
});
