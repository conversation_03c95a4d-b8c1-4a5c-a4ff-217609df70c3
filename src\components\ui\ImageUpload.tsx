import React, { useState } from 'react';
import { Upload, X } from 'lucide-react';
import Button from './Button';

interface ImageUploadProps {
  id: string;
  label?: string;
  onChange: (file: File | null) => void;
  error?: string;
  className?: string;
  required?: boolean;
  accept?: string;
  previewUrl?: string;
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  id,
  label,
  onChange,
  error,
  className = '',
  required = false,
  accept = 'image/*',
  previewUrl,
}) => {
  const [preview, setPreview] = useState<string | null>(previewUrl || null);
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    
    if (file) {
      const reader = new FileReader();
      reader.onload = () => {
        setPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
      onChange(file);
    } else {
      setPreview(null);
      onChange(null);
    }
  };
  
  const handleRemove = () => {
    setPreview(null);
    onChange(null);
  };
  
  return (
    <div className={`mb-4 ${className}`}>
      {label && (
        <label htmlFor={id} className="block text-sm font-medium text-gray-700 mb-1">
          {label} {required && <span className="text-error-600">*</span>}
        </label>
      )}
      
      {preview ? (
        <div className="relative">
          <img
            src={preview}
            alt="Preview"
            className="w-full h-48 object-cover rounded-md"
          />
          <button
            type="button"
            onClick={handleRemove}
            className="absolute top-2 right-2 bg-white rounded-full p-1 shadow-md hover:bg-gray-100"
          >
            <X size={16} className="text-gray-700" />
          </button>
        </div>
      ) : (
        <div className="border-2 border-dashed border-gray-300 rounded-md p-6 flex flex-col items-center justify-center">
          <Upload size={24} className="text-gray-400 mb-2" />
          <p className="text-sm text-gray-500 mb-2">Click to upload an image</p>
          <input
            id={id}
            type="file"
            accept={accept}
            onChange={handleChange}
            className="hidden"
          />
          <Button
            variant="outline"
            size="sm"
            onClick={() => document.getElementById(id)?.click()}
          >
            Select Image
          </Button>
        </div>
      )}
      
      {error && <p className="mt-1 text-sm text-error-600">{error}</p>}
    </div>
  );
};

export default ImageUpload;