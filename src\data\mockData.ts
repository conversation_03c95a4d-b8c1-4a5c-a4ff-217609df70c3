import { User, Car, Driver, Booking } from '../types';

export const mockUsers: User[] = [
  {
    id: 'user-1',
    email: '<EMAIL>',
    name: '<PERSON>',
    role: 'client',
    licenseImageUrl: 'https://images.pexels.com/photos/211290/pexels-photo-211290.jpeg',
    licenseVerificationStatus: 'verified',
    createdAt: '2024-01-01T00:00:00.000Z',
  },
  {
    id: 'user-2',
    email: '<EMAIL>',
    name: '<PERSON>',
    role: 'owner',
    phoneNumber: '+**********',
    isPhoneVerified: true,
    createdAt: '2024-01-02T00:00:00.000Z',
  },
  {
    id: 'user-3',
    email: '<EMAIL>',
    name: '<PERSON>',
    role: 'driver',
    phoneNumber: '+**********',
    isPhoneVerified: true,
    licenseImageUrl: 'https://images.pexels.com/photos/1300402/pexels-photo-1300402.jpeg',
    licenseVerificationStatus: 'verified',
    createdAt: '2024-01-03T00:00:00.000Z',
  },
  {
    id: 'user-4',
    email: '<EMAIL>',
    name: '<PERSON>',
    role: 'driver',
    phoneNumber: '+**********',
    isPhoneVerified: true,
    licenseImageUrl: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg',
    licenseVerificationStatus: 'verified',
    createdAt: '2024-01-04T00:00:00.000Z',
  },
  {
    id: 'user-5',
    email: '<EMAIL>',
    name: 'David Brown',
    role: 'driver',
    phoneNumber: '+**********',
    isPhoneVerified: true,
    licenseImageUrl: 'https://images.pexels.com/photos/1681010/pexels-photo-1681010.jpeg',
    licenseVerificationStatus: 'verified',
    createdAt: '2024-01-05T00:00:00.000Z',
  },
];

export const mockDrivers: Driver[] = [
  {
    id: 'driver-1',
    userId: 'user-3',
    name: 'Michael Johnson',
    age: 32,
    experience: 10,
    profileImage: 'https://images.pexels.com/photos/1300402/pexels-photo-1300402.jpeg',
    licenseNumber: '**********',
    licenseVerificationStatus: 'verified',
    location: 'Downtown Tech District',
    pricePerHour: 25,
    rating: 4.8,
    reviews: 124,
    specialties: ['City Driving', 'Long Distance', 'Airport Transfers'],
    availabilityNotes: 'Available weekdays from 8 AM to 6 PM',
    isAvailable: true,
    createdAt: '2024-02-01T00:00:00.000Z',
  },
  {
    id: 'driver-2',
    userId: 'user-4',
    name: 'Sarah Williams',
    age: 29,
    experience: 7,
    profileImage: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg',
    licenseNumber: '**********',
    licenseVerificationStatus: 'verified',
    location: 'Westside Shopping Center',
    pricePerHour: 22,
    rating: 4.9,
    reviews: 98,
    specialties: ['City Driving', 'Event Transportation', 'Tourist Guide'],
    availabilityNotes: 'Available all days from 10 AM to 8 PM',
    isAvailable: true,
    createdAt: '2024-02-05T00:00:00.000Z',
  },
  {
    id: 'driver-3',
    userId: 'user-5',
    name: 'David Brown',
    age: 35,
    experience: 12,
    profileImage: 'https://images.pexels.com/photos/1681010/pexels-photo-1681010.jpeg',
    licenseNumber: '**********',
    licenseVerificationStatus: 'verified',
    location: 'North Tech Park',
    pricePerHour: 28,
    rating: 4.7,
    reviews: 156,
    specialties: ['Luxury Vehicles', 'Corporate Events', 'Airport Transfers'],
    availabilityNotes: 'Available weekends and evenings after 6 PM',
    isAvailable: true,
    createdAt: '2024-02-10T00:00:00.000Z',
  },
];

export const mockBookings: Booking[] = [
  {
    id: 'booking-1',
    userId: 'user-1',
    itemType: 'car',
    itemId: 'car-1',
    startTime: '2024-03-15T10:00:00.000Z',
    endTime: '2024-03-15T14:00:00.000Z',
    totalPrice: 60,
    status: 'completed',
    createdAt: '2024-03-10T00:00:00.000Z',
  },
  {
    id: 'booking-2',
    userId: 'user-1',
    itemType: 'driver',
    itemId: 'driver-1',
    startTime: '2024-03-20T09:00:00.000Z',
    endTime: '2024-03-20T13:00:00.000Z',
    totalPrice: 100,
    status: 'confirmed',
    createdAt: '2024-03-15T00:00:00.000Z',
  },
];

export const mockCars: Car[] = [
  {
    id: 'car-1',
    ownerId: 'user-2',
    make: 'Toyota',
    model: 'Camry',
    year: 2022,
    images: [
      'https://images.pexels.com/photos/3729464/pexels-photo-3729464.jpeg',
      'https://images.pexels.com/photos/8434604/pexels-photo-8434604.jpeg'
    ],
    description: 'Well-maintained Toyota Camry with excellent fuel efficiency. Perfect for city driving.',
    features: ['Automatic Transmission', 'Bluetooth', 'Backup Camera', 'USB Charging'],
    location: 'Downtown Tech District',
    pricePerHour: 15,
    availabilityNotes: 'Available weekdays from 9 AM to 5 PM.',
    isActive: true,
    createdAt: '2024-02-01T00:00:00.000Z',
  },
  {
    id: 'car-2',
    ownerId: 'user-2',
    make: 'Honda',
    model: 'Civic',
    year: 2021,
    images: [
      'https://images.pexels.com/photos/3752169/pexels-photo-3752169.jpeg',
      'https://images.pexels.com/photos/5214413/pexels-photo-5214413.jpeg'
    ],
    description: 'Reliable Honda Civic with low mileage. Good for both city and highway driving.',
    features: ['Fuel Efficient', 'Apple CarPlay', 'Android Auto', 'Cruise Control'],
    location: 'Westside Shopping Center',
    pricePerHour: 12,
    availabilityNotes: 'Available all days from 8 AM to 8 PM.',
    isActive: true,
    createdAt: '2024-02-05T00:00:00.000Z',
  },
  {
    id: 'car-3',
    ownerId: 'user-2',
    make: 'Tesla',
    model: 'Model 3',
    year: 2023,
    images: [
      'https://images.pexels.com/photos/12309097/pexels-photo-12309097.jpeg',
      'https://images.pexels.com/photos/13533313/pexels-photo-13533313.jpeg'
    ],
    description: 'Electric Tesla Model 3 with full self-driving capability. Smooth and quiet ride with zero emissions.',
    features: ['Electric', 'Autopilot', 'Premium Sound System', 'Heated Seats'],
    location: 'North Tech Park',
    pricePerHour: 25,
    availabilityNotes: 'Available weekends only.',
    isActive: true,
    createdAt: '2024-02-10T00:00:00.000Z',
  },
  {
    id: 'car-4',
    ownerId: 'user-2',
    make: 'BMW',
    model: 'X5',
    year: 2022,
    images: [
      'https://images.pexels.com/photos/892522/pexels-photo-892522.jpeg',
      'https://images.pexels.com/photos/170811/pexels-photo-170811.jpeg'
    ],
    description: 'Luxury BMW X5 SUV with spacious interior and powerful engine. Perfect for family trips.',
    features: ['Leather Seats', 'Panoramic Sunroof', 'Navigation System', 'Premium Audio'],
    location: 'Eastside Financial District',
    pricePerHour: 30,
    availabilityNotes: 'Available Fridays through Mondays.',
    isActive: true,
    createdAt: '2024-02-15T00:00:00.000Z',
  },
  {
    id: 'car-5',
    ownerId: 'user-2',
    make: 'Ford',
    model: 'F-150',
    year: 2021,
    images: [
      'https://images.pexels.com/photos/2533092/pexels-photo-2533092.jpeg',
      'https://images.pexels.com/photos/2676451/pexels-photo-2676451.jpeg'
    ],
    description: 'Rugged Ford F-150 pickup truck. Great for hauling and outdoor adventures.',
    features: ['4x4', 'Towing Package', 'Bed Liner', 'Backup Camera'],
    location: 'South Industrial Area',
    pricePerHour: 22,
    availabilityNotes: 'Available on demand with 1-day notice.',
    isActive: true,
    createdAt: '2024-02-20T00:00:00.000Z',
  },
  {
    id: 'car-6',
    ownerId: 'user-2',
    make: 'Jeep',
    model: 'Wrangler',
    year: 2023,
    images: [
      'https://images.pexels.com/photos/13447251/pexels-photo-13447251.jpeg',
      'https://images.pexels.com/photos/5214413/pexels-photo-5214413.jpeg'
    ],
    description: 'Adventure-ready Jeep Wrangler. Perfect for off-road adventures and summer fun with removable top.',
    features: ['4x4', 'Removable Top', 'Off-Road Tires', 'Trail Rated'],
    location: 'Mountain View Apartments',
    pricePerHour: 24,
    availabilityNotes: 'Available weekends and holidays.',
    isActive: true,
    createdAt: '2024-02-25T00:00:00.000Z',
  },
];