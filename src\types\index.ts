export interface User {
  id: string;
  email: string;
  name: string;
  role: 'client' | 'owner' | 'driver';
  phoneNumber?: string;
  isPhoneVerified?: boolean;
  licenseImageUrl?: string;
  licenseVerificationStatus?: 'pending' | 'verified' | 'rejected';
  createdAt: string;
}

export interface Car {
  id: string;
  ownerId: string;
  make: string;
  model: string;
  year: number;
  images: string[];
  description: string;
  features: string[];
  location: string;
  pricePerHour: number;
  availabilityNotes: string;
  isActive: boolean;
  createdAt: string;
}

export interface Driver {
  id: string;
  userId: string;
  name: string;
  age: number;
  experience: number; // in years
  profileImage: string;
  licenseNumber: string;
  licenseVerificationStatus: 'pending' | 'verified' | 'rejected';
  location: string;
  pricePerHour: number;
  rating: number;
  reviews: number;
  specialties: string[];
  availabilityNotes: string;
  isAvailable: boolean;
  createdAt: string;
}

export interface Booking {
  id: string;
  userId: string;
  itemType: 'car' | 'driver';
  itemId: string;
  startTime: string;
  endTime: string;
  totalPrice: number;
  status: 'pending' | 'confirmed' | 'completed' | 'cancelled';
  createdAt: string;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

export interface AppState {
  auth: AuthState;
  cars: Car[];
  filteredCars: Car[];
  selectedCar: Car | null;
  drivers: Driver[];
  filteredDrivers: Driver[];
  selectedDriver: Driver | null;
  bookings: Booking[];
  isLoading: boolean;
  error: string | null;
}