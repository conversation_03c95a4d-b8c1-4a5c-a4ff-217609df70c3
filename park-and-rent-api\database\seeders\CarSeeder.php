<?php

namespace Database\Seeders;

use App\Models\Car;
use App\Models\User;
use Illuminate\Database\Seeder;

class CarSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the owner user
        $owner = User::where('email', '<EMAIL>')->first();

        if (!$owner) {
            return;
        }

        // Create sample cars
        Car::create([
            'owner_id' => $owner->id,
            'make' => 'Toyota',
            'model' => 'Corolla',
            'year' => 2020,
            'images' => [
                'https://example.com/car1_1.jpg',
                'https://example.com/car1_2.jpg',
            ],
            'description' => 'A reliable and fuel-efficient sedan perfect for city driving.',
            'features' => [
                'Air Conditioning',
                'Bluetooth',
                'Backup Camera',
                'USB Ports',
            ],
            'location' => 'Kigali, Rwanda',
            'price_per_hour' => 15000, // RWF
            'availability_notes' => 'Available on weekdays and weekends',
            'is_active' => true,
        ]);

        Car::create([
            'owner_id' => $owner->id,
            'make' => 'Honda',
            'model' => 'CR-V',
            'year' => 2019,
            'images' => [
                'https://example.com/car2_1.jpg',
                'https://example.com/car2_2.jpg',
            ],
            'description' => 'Spacious SUV with excellent fuel economy and ample cargo space.',
            'features' => [
                'Air Conditioning',
                'Bluetooth',
                'Navigation System',
                'Sunroof',
                'Leather Seats',
            ],
            'location' => 'Kigali, Rwanda',
            'price_per_hour' => 20000, // RWF
            'availability_notes' => 'Available on weekends only',
            'is_active' => true,
        ]);

        Car::create([
            'owner_id' => $owner->id,
            'make' => 'Jeep',
            'model' => 'Wrangler',
            'year' => 2021,
            'images' => [
                'https://example.com/car3_1.jpg',
                'https://example.com/car3_2.jpg',
            ],
            'description' => 'Rugged 4x4 perfect for off-road adventures and exploring Rwanda\'s beautiful landscapes.',
            'features' => [
                'Four-Wheel Drive',
                'Removable Top',
                'Bluetooth',
                'Off-Road Tires',
                'Tow Package',
            ],
            'location' => 'Musanze, Rwanda',
            'price_per_hour' => 25000, // RWF
            'availability_notes' => 'Available for weekend trips',
            'is_active' => true,
        ]);
    }
}
