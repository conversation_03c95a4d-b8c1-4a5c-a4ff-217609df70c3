@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --color-primary: #4C684C;
  --color-secondary: #4C684C;
  --color-accent: #4C684C;
}

@layer base {
  html {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
      Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    scroll-behavior: smooth;
  }

  body {
    @apply bg-gray-50 text-gray-900;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold tracking-tight;
  }

  h1 {
    @apply text-3xl md:text-4xl;
    line-height: 1.2;
  }

  h2 {
    @apply text-2xl md:text-3xl;
    line-height: 1.25;
  }

  h3 {
    @apply text-xl md:text-2xl;
    line-height: 1.3;
  }

  p, li, td, th, blockquote {
    @apply leading-relaxed;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2;
  }

  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }

  .btn-secondary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }

  .btn-accent {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }

  .btn-outline {
    @apply btn bg-transparent border border-primary-600 text-primary-600 hover:bg-primary-50 focus:ring-primary-500;
  }

  .card {
    @apply bg-white rounded-lg shadow-md overflow-hidden transition-all hover:shadow-lg;
  }

  .input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500;
  }

  .label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }

  .container {
    @apply px-4 mx-auto max-w-7xl sm:px-6 lg:px-8;
  }
}