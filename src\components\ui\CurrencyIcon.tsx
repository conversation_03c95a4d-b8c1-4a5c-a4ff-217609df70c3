import React from 'react';

interface CurrencyIconProps {
  size?: number;
  className?: string;
}

/**
 * A custom currency icon component that displays the RWF symbol
 */
const CurrencyIcon: React.FC<CurrencyIconProps> = ({ size = 16, className = '' }) => {
  return (
    <div className={`inline-flex items-center justify-center ${className}`}>
      <span className="font-medium" style={{ fontSize: `${size * 0.8}px`, lineHeight: 1 }}>
        ₣
      </span>
    </div>
  );
};

export default CurrencyIcon;
