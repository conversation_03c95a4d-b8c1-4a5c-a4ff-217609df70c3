import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Upload, CheckCircle, Shield, AlertCircle } from 'lucide-react';
import MainLayout from '../../components/layout/MainLayout';
import Button from '../../components/ui/Button';
import ImageUpload from '../../components/ui/ImageUpload';
import { useAuth } from '../../context/AuthContext';

const AccountVerificationPage: React.FC = () => {
  const { user, updateUser, isLoading } = useAuth();
  const navigate = useNavigate();
  
  const [licenseFile, setLicenseFile] = useState<File | null>(null);
  const [uploadSuccess, setUploadSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const handleLicenseUpload = (file: File | null) => {
    setLicenseFile(file);
    setError(null);
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    
    if (!licenseFile) {
      setError('Please upload your driving license');
      return;
    }
    
    try {
      // In a real app, you would upload the file to your server/storage
      // and then update the user profile with the URL
      
      // Simulate successful upload
      const licenseImageUrl = URL.createObjectURL(licenseFile);
      
      await updateUser({
        licenseImageUrl,
        licenseVerificationStatus: 'pending',
      });
      
      setUploadSuccess(true);
      
      // Automatically redirect after 3 seconds
      setTimeout(() => {
        navigate('/');
      }, 3000);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred during verification');
    }
  };
  
  // If user is not a client, redirect to home
  if (user?.role !== 'client') {
    navigate('/');
    return null;
  }
  
  // If user already has a verified license, show success message
  if (user?.licenseVerificationStatus === 'verified') {
    return (
      <MainLayout>
        <div className="min-h-[calc(100vh-200px)] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
          <div className="max-w-md w-full text-center">
            <CheckCircle className="h-16 w-16 text-success-500 mx-auto mb-4" />
            <h1 className="text-3xl font-bold text-gray-900 mb-4">License Verified</h1>
            <p className="text-lg text-gray-600 mb-8">
              Your driving license has been successfully verified. You can now contact car owners and arrange rentals.
            </p>
            <Button onClick={() => navigate('/cars')}>
              Browse Available Cars
            </Button>
          </div>
        </div>
      </MainLayout>
    );
  }
  
  // If user has a pending license verification, show pending message
  if (user?.licenseVerificationStatus === 'pending') {
    return (
      <MainLayout>
        <div className="min-h-[calc(100vh-200px)] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
          <div className="max-w-md w-full text-center">
            <div className="h-16 w-16 mx-auto mb-4 rounded-full bg-yellow-100 flex items-center justify-center">
              <AlertCircle className="h-10 w-10 text-yellow-500" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Verification Pending</h1>
            <p className="text-lg text-gray-600 mb-8">
              Your driving license is currently being verified. This process usually takes 1-2 business days.
            </p>
            <Button onClick={() => navigate('/cars')}>
              Browse Available Cars
            </Button>
          </div>
        </div>
      </MainLayout>
    );
  }
  
  return (
    <MainLayout>
      <div className="min-h-[calc(100vh-200px)] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full">
          {uploadSuccess ? (
            <div className="text-center">
              <CheckCircle className="h-16 w-16 text-success-500 mx-auto mb-4" />
              <h1 className="text-3xl font-bold text-gray-900 mb-4">License Uploaded!</h1>
              <p className="text-lg text-gray-600 mb-8">
                Your driving license has been submitted for verification. We'll review it shortly.
              </p>
              <div className="bg-success-50 border border-success-200 rounded-lg p-4 mb-6">
                <p className="text-success-700">
                  You'll be redirected in a few seconds...
                </p>
              </div>
            </div>
          ) : (
            <>
              <div className="text-center mb-8">
                <Shield className="h-12 w-12 text-primary-600 mx-auto" />
                <h1 className="mt-4 text-3xl font-bold text-gray-900">Verify Your Account</h1>
                <p className="mt-2 text-gray-600">
                  Upload your driving license to verify your account and gain access to contact car owners.
                </p>
              </div>
              
              <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
                {error && (
                  <div className="mb-4 p-3 bg-error-50 border border-error-200 rounded-md">
                    <div className="flex">
                      <AlertCircle size={18} className="text-error-600 mr-2 flex-shrink-0" />
                      <p className="text-sm text-error-600">{error}</p>
                    </div>
                  </div>
                )}
                
                <form onSubmit={handleSubmit}>
                  <div className="mb-6">
                    <ImageUpload
                      id="license"
                      label="Driving License"
                      onChange={handleLicenseUpload}
                      required
                    />
                    <p className="text-sm text-gray-600 mt-2">
                      Please upload a clear image of your valid driving license.
                      We'll verify it to ensure the safety of our community.
                    </p>
                  </div>
                  
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                    <div className="flex">
                      <AlertCircle size={18} className="text-yellow-600 mr-2 flex-shrink-0 mt-0.5" />
                      <div>
                        <h3 className="text-sm font-medium text-yellow-800">Important Information</h3>
                        <p className="text-sm text-yellow-700 mt-1">
                          Your license will only be used for verification purposes and will be securely stored.
                          You won't be able to contact car owners until your license is verified.
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  <Button
                    type="submit"
                    fullWidth
                    disabled={isLoading || !licenseFile}
                  >
                    {isLoading ? 'Uploading...' : 'Submit for Verification'}
                  </Button>
                </form>
              </div>
            </>
          )}
        </div>
      </div>
    </MainLayout>
  );
};

export default AccountVerificationPage;